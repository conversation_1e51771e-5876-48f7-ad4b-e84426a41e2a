<script lang="ts" setup>
import { Color } from '@/enums/colorEnum'
import dayjs from 'dayjs'

// 定义评论数据类型
interface CommentData {
  avatarUrl: string
  commentContent: string
  commentId: number
  commentTime: string
  goodsId: number
  images: string
  nickName: string
  score: number
  userCode: string
  userId: number
}

// 定义组件props
interface Props {
  comment: CommentData
  showBorder?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  showBorder: true,
})

// 定义事件
const emit = defineEmits<{
  previewImage: [images: string[], index: number]
}>()

// 处理评价图片
const getCommentImages = (imagesStr: string) => {
  if (!imagesStr) return []
  const arr = imagesStr.split(',')
  // 最后一个元素如果为空，则去掉
  if (!arr[arr.length - 1]) {
    arr.pop()
  }
  return arr
}

// 预览评价图片
const handlePreviewImage = (images: string[], index: number) => {
  emit('previewImage', images, index)
}
</script>

<template>
  <view class="comment-item" :class="{ 'f-border-bottom': showBorder }">
    <view class="comment-header">
      <up-avatar class="comment-avatar" :size="30" :src="comment.avatarUrl"></up-avatar>
      <view class="comment-user-info">
        <view class="comment-username">{{ comment.nickName || '匿名用户' }}</view>
        <view class="comment-time">
          {{ dayjs(comment.commentTime).format('YYYY-MM-DD') }}
        </view>
      </view>
      <up-rate
        class="comment-rating"
        v-model="comment.score"
        :activeColor="Color.primary"
        size="16"
        readonly
        gutter="2"
      ></up-rate>
    </view>

    <view class="comment-content">{{ comment.commentContent }}</view>

    <view v-if="comment.images" class="comment-images">
      <view
        v-for="(img, imgIndex) in getCommentImages(comment.images)"
        :key="imgIndex"
        class="comment-image"
        @click="handlePreviewImage(getCommentImages(comment.images), imgIndex)"
      >
        <up-image :src="img" :width="80" :height="80" mode="aspectFill" class="rounded" />
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.comment-item {
  @apply mb-4 pb-4;
}

.comment-header {
  @apply flex items-center justify-between mb-2;
}

.comment-avatar {
  @apply shrink-0;
}

.comment-user-info {
  @apply ml-2 flex-1 overflow-hidden;
}

.comment-username {
  @apply font-bold text-sm;
}

.comment-time {
  @apply flex text-xs text-gray-500;
}

.comment-rating {
  @apply shrink-0;
}

.comment-content {
  @apply my-2;
}

.comment-images {
  @apply flex flex-wrap gap-2 mt-2;
}

.comment-image {
  @apply relative overflow-hidden rounded;
}

.f-border-bottom {
  @apply border-b border-gray-200;
}
</style>
